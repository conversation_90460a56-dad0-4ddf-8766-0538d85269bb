import {Button, message, Modal} from '@panda-design/components';
import {Flex, Space, Switch, Typography} from 'antd';
import {Dispatch, SetStateAction, useCallback} from 'react';
import {css} from '@emotion/css';
import DescriptionItem from '@/design/MCP/MCPDescriptionItem';
import {useMCPApplicationId} from '@/components/MCP/hooks';
import {loadMCPApplicationInfo} from '@/regions/mcp/mcpApplication';
import {MCPApplicationServer} from '@/types/mcp/application';
import {apiPutMCPServerEnable} from '@/api/mcp/application';
import {IconSubscribe} from '@/icons/mcp';
import MCPServerTypeTag from '@/components/MCP/MCPServerTypeTag';
import {apiPutApplicationUnsubscribe} from '@/api/mcp';
import MCPServerProtocolTypeTag from '@/components/MCP/MCPServerProtocolTypeTag';
import TagGroup from '@/components/MCP/TagGroup';
import {UserAvatarList} from '@/components/MCP/UserAvatarList';
import CollapsePanel from './CollapsePanel';

const textColorSecondary = css`
    color: #545454;
`;


interface Props {
    server: MCPApplicationServer;
    setActiveIndex: Dispatch<SetStateAction<string>>;
}
const ServerDescription = ({server, setActiveIndex}: Props) => {
    const applicationId = useMCPApplicationId();
    console.log(server, 'server');
    const handleChange = useCallback(
        async (checked: boolean) => Modal.confirm({
            content: `此操作会在应用下次获取MCP Server时生效，请确认是否${checked ? '打开' : '关闭'}？`,
            onOk: async () => {
                try {
                    await apiPutMCPServerEnable({
                        applicationId,
                        serverId: server?.id,
                        enable: checked,
                    });
                    loadMCPApplicationInfo({applicationId});
                }
                catch (e) {
                    message.error('操作失败');
                    throw e;
                }
            },
        }),
        [applicationId, server]
    );

    const handleUnsubscribe = useCallback(
        () => Modal.confirm({
            content: '取消订阅后所有配置会被删除，重新订阅需要重新配置。请确认是否取消订阅？',
            onOk: async () => {
                try {
                    await apiPutApplicationUnsubscribe({
                        applicationId,
                        serverId: server?.id,
                    });
                    setActiveIndex('0');
                    loadMCPApplicationInfo({applicationId});
                }
                catch (e) {
                    message.error('操作失败');
                    throw e;
                }
            },
        }),
        [applicationId, server?.id, setActiveIndex]
    );
    return (
        <CollapsePanel
            header={(
                <>{server?.name} MCP Server详情</>
            )}
            extra={
                <Space>
                    <Button
                        icon={<IconSubscribe />}
                        type="text"
                        onClick={handleUnsubscribe}
                    >
                        取消订阅
                    </Button>
                    <Flex align="center" gap={4} className={textColorSecondary}>
                        <Switch
                            size="small"
                            checked={server?.enable ?? false}
                            onChange={handleChange}
                        />
                        <span style={{whiteSpace: 'nowrap', color: '#000'}}>生效</span>
                    </Flex>
                </Space>
            }
            body={(
                <Flex vertical gap={12}>
                    <Space size={32}>
                        <DescriptionItem type="secondary" label="类型">
                            <MCPServerTypeTag type={server?.serverProtocolType} />
                        </DescriptionItem>
                        <DescriptionItem type="secondary" label="协议">
                            <MCPServerProtocolTypeTag type={server?.serverProtocolType} />
                        </DescriptionItem>
                        <DescriptionItem type="secondary" label="场景">
                            <TagGroup
                                color="gray"
                                maxNum={3}
                                labels={(server?.labels ?? []).map(item => ({id: item.id, label: item.labelValue}))}
                            />
                        </DescriptionItem>
                    </Space>
                    <Space size={32}>
                        <DescriptionItem type="secondary" label="部门">
                            <span>
                                {server?.departmentName || '暂无部门信息'}
                            </span>
                        </DescriptionItem>
                        <DescriptionItem type="secondary" label="最近更新时间">
                            <span>{ server?.tools[0]?.lastModifyTime || '暂无'}</span>
                        </DescriptionItem>
                        <DescriptionItem type="secondary" label="联系人">
                            <UserAvatarList users={server.contacts} max={2} />
                        </DescriptionItem>
                    </Space>
                    <Typography.Paragraph type="secondary">
                        {server?.description || '暂无描述'}
                    </Typography.Paragraph>
                </Flex>
            )}
        />
    );
};

export default ServerDescription;
